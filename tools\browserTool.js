import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';

class BrowserTool {
  constructor() {
    this.name = 'browserTool';
    this.description = 'Fetches content from a webpage. Supports getting visible text, links, images, and taking screenshots.';
    // The inputSchema helps the LLM understand what arguments to provide.
    this.inputSchema = {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'The fully qualified URL of the webpage to access (e.g., "https://example.com").',
        },
        action: {
          type: 'string',
          description: 'The action to perform. Supported: "getVisibleText", "getLinks", "getImages", "takeScreenshot".',
          enum: ['getVisibleText', 'getLinks', 'getImages', 'takeScreenshot'], // Explicitly list supported actions
        },
        fullPage: {
          type: 'boolean',
          description: 'For "takeScreenshot" action: Whether to take a full-page screenshot. Defaults to true.',
          default: true,
        },
      },
      required: ['url', 'action'],
    };
  }

  async execute(args) {
    const { url, action } = args;

    if (!url || typeof url !== 'string' || !url.startsWith('http')) {
      console.error(`${this.name}: Invalid or missing URL. URL: ${url}`);
      return { error: `Invalid or missing URL. Must be a fully qualified HTTP/HTTPS URL. Received: ${url}` };
    }
    // Validate action against the enum in inputSchema
    if (!this.inputSchema.properties.action.enum.includes(action)) {
      console.error(`${this.name}: Unsupported action: ${action}`);
      return { error: `Unsupported action: ${action}. Supported actions are: ${this.inputSchema.properties.action.enum.join(', ')}.` };
    }

    let browser = null;
    try {
      console.log(`${this.name}: Launching browser for action "${action}" on URL: ${url}`);
      browser = await puppeteer.launch({
        headless: true, // Run headless for server-side use
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
      });
      const page = await browser.newPage();
      
      // Set a reasonable timeout for navigation
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 }); // 30 seconds timeout

      if (action === 'getVisibleText') {
        console.log(`${this.name}: Page loaded. Extracting visible text with improved selection...`);
        const textContent = await page.evaluate(() => {
          // Remove elements that typically don't contain main content
          document.querySelectorAll(
            'script, style, noscript, svg, path, [aria-hidden="true"], header, footer, nav, aside, form, button, input, textarea, select, option, label, .ad, .advertisement, .banner, .popup, .modal, .cookie-consent, link[rel="stylesheet"], meta'
          ).forEach(el => el.remove());

          // Prioritize common main content containers
          const mainContentSelectors = ['article', 'main', '.main-content', '#main', '#content', '.content', 'body'];
          let targetElement = null;
          for (const selector of mainContentSelectors) {
            targetElement = document.querySelector(selector);
            if (targetElement) break;
          }
          
          if (!targetElement) targetElement = document.body; // Fallback to body

          // Select common content-bearing elements within the target
          const contentSelectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'p',
            'li', 
            'blockquote',
            'td', 'th', // Table content
            'span', // Sometimes spans contain meaningful text not in other block elements
            'div' // As a broader catch-all for text nodes directly in divs
          ];
          
          let extractedTexts = [];
          // Convert NodeList to Array to use array methods like .includes()
          const elementsNodeList = targetElement.querySelectorAll(contentSelectors.join(', '));
          const elementsArray = Array.from(elementsNodeList);

          elementsArray.forEach(el => {
            // Check if the element is likely visible and contains meaningful text
            if (el.offsetParent !== null && !el.closest('select, datalist, details:not([open])')) {
              // Get innerText to respect visibility and some formatting, then trim
              let text = el.innerText?.trim(); 
              if (text && text.length > 10) { // Only consider reasonably long text segments
                // Avoid adding text if it's just a duplicate of its child's text already captured
                let isDuplicateOfChild = false;
                if (el.children.length === 1 && elementsArray.includes(el.children[0])) {
                   if (el.children[0].innerText?.trim() === text) {
                      isDuplicateOfChild = true;
                   }
                }
                if(!isDuplicateOfChild) {
                  extractedTexts.push(text);
                }
              }
            }
          });

          // If specific selectors yielded little, try a more general approach on the target element
          if (extractedTexts.join(' ').length < 200 && targetElement) { // Arbitrary threshold
              let fallbackText = targetElement.innerText || "";
              // Split by what might be paragraph breaks, then filter and trim
              extractedTexts = fallbackText.split(/\n\s*\n|\r\n\s*\r\n/) 
                                        .map(p => p.replace(/\s\s+/g, ' ').trim())
                                        .filter(p => p.length > 20); // Keep paragraphs with some substance
          }
          
          // Deduplicate while preserving order (simple approach)
          const uniqueTexts = [...new Set(extractedTexts)];
          return uniqueTexts.join('\n\n'); // Join paragraphs with double newlines for structure
        });
        
        console.log(`${this.name}: Extracted text (length: ${textContent?.length || 0}).`);
        return { text: textContent || "" };
      } else if (action === 'getLinks') {
        console.log(`${this.name}: Extracting links from URL: ${url}`);
        const links = await page.evaluate(() => {
            const linkElements = document.querySelectorAll('a');
            return Array.from(linkElements).map(link => link.href);
        });
        console.log(`${this.name}: Extracted ${links.length} links.`);
        return { links };
      } else if (action === 'getImages') {
        console.log(`${this.name}: Extracting images from URL: ${url}`);
        const images = await page.evaluate(() => {
            const imageElements = document.querySelectorAll('img');
            return Array.from(imageElements).map(img => img.src);
        });
        console.log(`${this.name}: Extracted ${images.length} images.`);
        return { images };
      } else if (action === 'takeScreenshot') {
        console.log(`${this.name}: Taking screenshot of URL: ${url}`);
        const fullPage = args.fullPage !== false; // Default to fullPage: true if not specified.
        const screenshotBuffer = await page.screenshot({ fullPage });
        console.log(`${this.name}: Screenshot taken.`);

        // Define screenshots directory
        const screenshotsDir = path.join(process.cwd(), 'public', 'screenshots');

        // Ensure directory exists
        if (!fs.existsSync(screenshotsDir)) {
          fs.mkdirSync(screenshotsDir, { recursive: true });
          console.log(`${this.name}: Created screenshots directory at ${screenshotsDir}`);
        }

        // Generate unique filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `screenshot_${timestamp}.png`;
        const filePath = path.join(screenshotsDir, filename);

        // Save the screenshot
        fs.writeFileSync(filePath, screenshotBuffer);
        console.log(`${this.name}: Screenshot saved to ${filePath}`);

        // Return the path relative to the project root, or a web-accessible path
        const relativePath = path.join('public', 'screenshots', filename);
        return { screenshotPath: relativePath, base64Screenshot: screenshotBuffer.toString('base64') }; // Return path and base64
      }

    } catch (error) {
      console.error(`${this.name}: Error during Puppeteer operation for ${url}:`, error);
      return { error: `Failed to process URL "${url}". Details: ${error.message}` };
    } finally {
      if (browser) {
        console.log(`${this.name}: Closing browser for URL: ${url}`);
        await browser.close();
      }
    }
  }

  getCommandsDocumentation() {
    return `- execute '{"url": "<URL_to_visit>", "action": "getVisibleText"}': Fetches the visible text content from the specified URL. The URL must be fully qualified (e.g., "https://example.com").
- execute '{"url": "<URL_to_visit>", "action": "getLinks"}': Extracts all links (anchor hrefs) from the specified URL.
- execute '{"url": "<URL_to_visit>", "action": "getImages"}': Extracts all image sources (img srcs) from the specified URL.
- execute '{"url": "<URL_to_visit>", "action": "takeScreenshot", "fullPage": <true_or_false_defaults_to_true>}': Takes a screenshot of the page (optionally the full page), saves it to 'public/screenshots/', and returns the file path and base64 encoded data.`;
  }
}

export const browserTool = new BrowserTool();
