// test-browser-tool.js - Test BrowserTool integration

import { processMessage } from './agentLogic-simplified.js';

async function testBrowserTool() {
  console.log('Testing BrowserTool integration...\n');

  try {
    // Wait for default agent initialization
    console.log('Waiting for default agent initialization...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test 1: Check if browserTool is available
    console.log('=== Test 1: Tool availability ===');
    const toolList = await processMessage('what tools do you have available', { sessionId: 'browser-test' });
    console.log('Contains browserTool:', toolList.text?.includes('browserTool') || toolList.text?.includes('browser'));
    
    // Test 2: Get visible text from a webpage
    console.log('\n=== Test 2: Get visible text ===');
    const textRequest = await processMessage('get the visible text from https://example.com', { sessionId: 'browser-test' });
    console.log('Text request completed');
    console.log('Has error:', textRequest.error || false);
    console.log('Response length:', textRequest.text?.length || 0);
    
    if (textRequest.text && textRequest.text.length > 100) {
      console.log('✅ Successfully retrieved webpage text');
      console.log('Preview:', textRequest.text.substring(0, 200) + '...');
    } else {
      console.log('❌ Failed to retrieve webpage text or response too short');
    }

    // Test 3: Get links from a webpage
    console.log('\n=== Test 3: Get links ===');
    const linksRequest = await processMessage('get all the links from https://example.com', { sessionId: 'browser-test' });
    console.log('Links request completed');
    console.log('Has error:', linksRequest.error || false);
    console.log('Response mentions links:', linksRequest.text?.includes('link') || linksRequest.text?.includes('href'));

    // Test 4: Take a screenshot
    console.log('\n=== Test 4: Take screenshot ===');
    const screenshotRequest = await processMessage('take a screenshot of https://example.com', { sessionId: 'browser-test' });
    console.log('Screenshot request completed');
    console.log('Has error:', screenshotRequest.error || false);
    console.log('Response mentions screenshot:', screenshotRequest.text?.includes('screenshot') || screenshotRequest.text?.includes('image'));

    console.log('\n🎉 BrowserTool test completed!');
    
    // Summary
    const allSuccessful = !textRequest.error && !linksRequest.error && !screenshotRequest.error;
    if (allSuccessful) {
      console.log('\n✅ SUCCESS: BrowserTool is working correctly!');
      console.log('- Tool is available and integrated');
      console.log('- Can fetch webpage text');
      console.log('- Can extract links');
      console.log('- Can take screenshots');
    } else {
      console.log('\n⚠️  Some browser operations may have issues');
      console.log('Check the individual test results above');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

testBrowserTool();
