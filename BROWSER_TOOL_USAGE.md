# BrowserTool Usage Guide

## Overview
The BrowserTool has been successfully integrated into your Vari3 AI agent. It uses Puppeteer to interact with web pages and can perform various web scraping and automation tasks.

## Available Actions

### 1. Get Visible Text (`getVisibleText`)
Extracts the main readable content from a webpage.

**User Examples:**
- "Get the text from https://example.com"
- "What does the content on https://news.ycombinator.com say?"
- "Extract the visible text from https://github.com/microsoft/vscode"

**LLM Tool Call Format:**
```
TOOL_CALL: browserTool:execute '{"url": "https://example.com", "action": "getVisibleText"}'
```

### 2. Get Links (`getLinks`)
Extracts all hyperlinks (anchor tags) from a webpage.

**User Examples:**
- "Get all the links from https://example.com"
- "What links are available on https://reddit.com?"
- "Extract all URLs from https://awesome-lists.com"

**LLM Tool Call Format:**
```
TOOL_CALL: browserTool:execute '{"url": "https://example.com", "action": "getLinks"}'
```

### 3. Get Images (`getImages`)
Extracts all image sources from a webpage.

**User Examples:**
- "Get all images from https://unsplash.com"
- "What images are on https://example.com?"
- "Extract image URLs from https://gallery-website.com"

**LLM Tool Call Format:**
```
TOOL_CALL: browserTool:execute '{"url": "https://example.com", "action": "getImages"}'
```

### 4. Take Screenshot (`takeScreenshot`)
Captures a screenshot of the webpage and saves it to the public/screenshots directory.

**User Examples:**
- "Take a screenshot of https://example.com"
- "Capture an image of https://google.com"
- "Screenshot the page at https://github.com"

**LLM Tool Call Format:**
```
TOOL_CALL: browserTool:execute '{"url": "https://example.com", "action": "takeScreenshot"}'
```

**Optional Parameters:**
- `fullPage`: Boolean (default: true) - Whether to capture the full page or just the viewport
```
TOOL_CALL: browserTool:execute '{"url": "https://example.com", "action": "takeScreenshot", "fullPage": false}'
```

## Technical Details

### Integration Status
✅ **Successfully integrated** into your Vari3 agent
- Imported in `agentLogic-simplified.js`
- Added to the default agent during initialization
- Available through the SystemPromptManager for dynamic tool documentation

### Tool Configuration
- **Name**: `browserTool`
- **Description**: "Fetches content from a webpage. Supports getting visible text, links, images, and taking screenshots."
- **Uses**: Puppeteer for browser automation
- **Headless**: Runs in headless mode for server-side use
- **Timeout**: 30 seconds for page navigation

### File Locations
- **Tool Implementation**: `tools/browserTool.js`
- **Screenshots Saved To**: `public/screenshots/`
- **Screenshot Format**: PNG files with timestamp naming

### Error Handling
The tool includes robust error handling for:
- Invalid URLs (must be fully qualified HTTP/HTTPS)
- Unsupported actions
- Network timeouts
- Browser launch failures
- Page navigation errors

### Security Features
- Runs with security flags: `--no-sandbox`, `--disable-setuid-sandbox`, `--disable-dev-shm-usage`
- Validates URLs before processing
- Automatically closes browser instances after use

## Usage Tips

### For Users
1. **Always use full URLs** including `https://` or `http://`
2. **Be specific about what you want** - text, links, images, or screenshots
3. **Wait for completion** - web scraping can take a few seconds

### For LLM Integration
1. **URL Validation**: The tool requires fully qualified URLs
2. **Action Validation**: Only the four supported actions are allowed
3. **JSON Format**: Arguments must be valid JSON strings
4. **Error Responses**: The tool returns structured error messages for debugging

## Example Conversations

**User**: "What's on the front page of Hacker News?"
**Agent**: Uses `getVisibleText` action to fetch and summarize the content

**User**: "Take a screenshot of Google's homepage"
**Agent**: Uses `takeScreenshot` action and returns the file path

**User**: "Get all the links from the Python.org website"
**Agent**: Uses `getLinks` action to extract and list all hyperlinks

## Troubleshooting

### Common Issues
1. **"Invalid URL" errors**: Ensure URLs start with `http://` or `https://`
2. **Timeout errors**: Some websites may take longer to load
3. **Permission errors**: Screenshots require write access to `public/screenshots/`

### Performance Notes
- Each browser operation launches a new Puppeteer instance
- Screenshots are saved locally and can accumulate over time
- Large pages may take longer to process

## Integration Complete! 🎉

Your BrowserTool is now fully integrated and ready to use. Users can ask for web content, links, images, or screenshots, and the AI agent will automatically use the appropriate browser action to fulfill their requests.
